<!DOCTYPE html>
<html lang="it">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contatti - Lira Valuta</title>
  <meta name="description" content="Lira Valuta offre soluzioni FinTech avanzate: elaborazione pagamenti, trasferimenti sicuri, conti multivaluta, analisi dati e piattaforme di prestito per PMI.">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
  
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <style>
    body { font-family: 'Lato', sans-serif; color: #212529; }
    h1, h2, h3, h4, h5, h6, .navbar-brand { font-family: 'Montserrat', sans-serif; font-weight: 600; }
    .btn-primary { background-color: #0D47A1; border-color: #0D47A1; }
    .btn-primary:hover { background-color: #0B3A84; border-color: #0B3A84; }
    .btn-secondary { background-color: #42A5F5; border-color: #42A5F5; }
    .btn-secondary:hover { background-color: #1E88E5; border-color: #1E88E5; }
    .navbar-light .navbar-nav .nav-link { color: rgba(0,0,0,.7); }
    .navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link.active { color: #0D47A1; }
  </style>
</head>
<body>
  <header>
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
      <div class="container">
        <a class="navbar-brand font-weight-bold" href="/">Lira Valuta</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ml-auto">
            <li class="nav-item">
              <a class="nav-link" href="/">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="chi-siamo.html">Chi Siamo</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="servizi.html">Servizi</a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="contatti.html">Contatti</a>
            </li>
          </ul>
          <a href="contatti.html" class="btn btn-primary ml-lg-3">Richiedi una Demo</a>
        </div>
      </div>
    </nav>
  </header>

  <main class="container py-5">
    <section class="contact-section mb-5">
      <h1 class="text-center mb-4">Mettiti in Contatto con Noi</h1>
      <p class="lead text-center text-muted mb-5">Siamo qui per rispondere alle tue domande. Compila il modulo o utilizza i contatti diretti.</p>

      <div class="row">
        <div class="col-lg-7 mb-4 mb-lg-0">
          <div class="card shadow-sm h-100 bordered-card">
            <div class="card-body p-4">
              <h3 class="card-title mb-4">Inviaci un Messaggio</h3>
              <form>
                <div class="form-group">
                  <label for="nome">Nome Completo</label>
                  <input type="text" class="form-control" id="nome" placeholder="Il tuo nome" required>
                </div>
                <div class="form-group">
                  <label for="azienda">Azienda (opzionale)</label>
                  <input type="text" class="form-control" id="azienda" placeholder="Nome della tua azienda">
                </div>
                <div class="form-group">
                  <label for="email">Indirizzo Email</label>
                  <input type="email" class="form-control" id="email" placeholder="La tua email" required>
                </div>
                <div class="form-group">
                  <label for="telefono">Numero di Telefono (opzionale)</label>
                  <input type="tel" class="form-control" id="telefono" placeholder="Il tuo numero di telefono">
                </div>
                <div class="form-group">
                  <label for="messaggio">Il tuo Messaggio</label>
                  <textarea class="form-control" id="messaggio" rows="5" placeholder="Scrivi qui il tuo messaggio..." required></textarea>
                </div>
                <button type="submit" class="btn btn-primary btn-block">Invia Messaggio</button>
              </form>
            </div>
          </div>
        </div>

        <div class="col-lg-5">
          <div class="card shadow-sm h-100 bordered-card">
            <div class="card-body p-4">
              <h3 class="card-title mb-4">Contatti Diretti</h3>
              <ul class="list-unstyled mb-4">
                <li class="mb-3"><i class="fas fa-envelope text-primary mr-2"></i> Email: <a href="mailto:<EMAIL>" class="text-dark"><EMAIL></a></li>
                <li class="mb-3"><i class="fas fa-phone-alt text-primary mr-2"></i> Telefono: <a href="tel:+390287345678" class="text-dark">+39 02 8734 5678</a></li>
                <li class="mb-3"><i class="fas fa-map-marker-alt text-primary mr-2"></i> Indirizzo: 2847 Viale Certosa, Piano 3, 20151 Milano, Italia</li>
              </ul>

              <h4 class="mt-4 mb-3">Dove Trovarci</h4>
              <div class="map-container mb-3" style="height: 256px; width: 100%; overflow: hidden; border-radius: .25rem;">
                <img src="images/contact-map-italy-milan.jpeg" alt="Mappa della posizione di Lira Valuta a Milano, Italia" class="img-fluid w-100 h-100" style="object-fit: cover;" width="640" height="384">
              </div>
              <p class="small text-muted text-center">Nota: L'immagine della mappa è puramente indicativa.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main><footer class="bg-dark text-light pt-5 pb-4">
    <div class="container text-center text-md-left">
      <div class="row">
        <div class="col-md-3 col-lg-4 col-xl-3 mx-auto mb-4">
          <h6 class="text-uppercase font-weight-bold">Lira Valuta</h6>
          <hr class="bg-light mb-4 mt-0 d-inline-block mx-auto" style="width: 60px;">
          <p>Soluzioni finanziarie digitali per semplificare il tuo business e accelerare la tua crescita globale.</p>
        </div>
        <div class="col-md-2 col-lg-2 col-xl-2 mx-auto mb-4">
          <h6 class="text-uppercase font-weight-bold">Link Utili</h6>
          <hr class="bg-light mb-4 mt-0 d-inline-block mx-auto" style="width: 60px;">
          <p><a href="chi-siamo.html" class="text-light">Chi Siamo</a></p>
          <p><a href="servizi.html" class="text-light">Servizi</a></p>
          <p><a href="contatti.html" class="text-light">Contatti</a></p>
          <p><a href="#" class="text-light">Lavora con noi</a></p>
        </div>
        <div class="col-md-4 col-lg-3 col-xl-3 mx-auto mb-md-0 mb-4">
          <h6 class="text-uppercase font-weight-bold">Contatti</h6>
          <hr class="bg-light mb-4 mt-0 d-inline-block mx-auto" style="width: 60px;">
          <p><i class="fas fa-home mr-3"></i> Viale Certosa 2847, Milano, IT</p>
          <p><i class="fas fa-envelope mr-3"></i> <EMAIL></p>
          <p><i class="fas fa-phone mr-3"></i> +39 02 8734 5678</p>
        </div>
      </div>
    </div>
    <div class="container-fluid bg-secondary-dark-color">
      <div class="container">
        <div class="row py-3 d-flex align-items-center">
          <div class="col-md-6 col-lg-7 text-center text-md-left">
            <span>© 2024 Lira Valuta S.r.l. | Tutti i diritti riservati.</span>
            <a href="informativa-sulla-privacy.html" class="text-light ml-3">Privacy</a>
            <a href="termini-e-condizioni.html" class="text-light ml-3">Termini</a>
          </div>
          <div class="col-md-6 col-lg-5 text-center text-md-right">
            
          </div>
        </div>
      </div>
    </div>
  </footer>

  <div id="cookie-consent-banner" class="p-3 bg-dark text-light fixed-bottom d-none">
    <div class="container d-flex justify-content-between align-items-center flex-wrap">
        <p class="mb-0 mr-3">Questo sito utilizza cookie per migliorare la tua esperienza di navigazione. Accettando, acconsenti al nostro utilizzo dei cookie.</p>
        <button id="cookie-accept-btn" class="btn btn-primary btn-sm mt-2 mt-md-0">Accetta</button>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    (function() {
      const consentBanner = document.getElementById('cookie-consent-banner');
      const acceptBtn = document.getElementById('cookie-accept-btn');
      // Generate a simple random string for the cookie name for this session context
      const cookieName = 'cookieConsent_' + Math.random().toString(36).substring(2, 10);

      if (!localStorage.getItem(cookieName)) {
        consentBanner.classList.remove('d-none');
      }

      acceptBtn.addEventListener('click', function() {
        localStorage.setItem(cookieName, 'true');
        consentBanner.style.display = 'none';
      });
    })();
  </script>
</body>
</html>