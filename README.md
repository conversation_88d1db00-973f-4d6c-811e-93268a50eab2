# Lira Valuta - Fintech Company Website

This project is a static website for Lira Valuta, a fictional Italian Fintech company. The website is designed to showcase the company's services, provide information about the company, and offer a point of contact.

## Technical Details

The website is built using fundamental web technologies and a popular front-end framework. Here’s a breakdown of the technical implementation:

### Core Technologies

*   **HTML5:** The structure and content of all pages are written in semantic HTML5.
*   **CSS3:** Custom styling is applied using CSS3, in addition to the Bootstrap framework. The custom styles are embedded within `<style>` tags in the head of each HTML file.
*   **JavaScript (ES6):** The website uses vanilla JavaScript for a simple cookie consent banner. The script uses `localStorage` to remember the user's choice.
*   **PHP:** The main page has a `.php` extension, but it does not contain any server-side PHP code. It is a static HTML file served as a PHP file, which might be a remnant of a previous development plan or a server-side requirement.

### Front-end Framework and Libraries

*   **Bootstrap 4.6.2:** The website's layout is fully responsive and built on the Bootstrap framework. It uses Bootstrap's grid system, components (like navbars, cards, and carousels), and utilities. The framework's CSS and JavaScript are loaded from a CDN.
*   **jQuery 3.5.1/3.6.0:** The project uses jQuery, primarily as a dependency for Bootstrap's JavaScript components. It is also loaded from a CDN.
*   **Font Awesome:** The HTML includes a link to a Font Awesome kit, but the kit URL is a placeholder (`your-font-awesome-kit.js`). This suggests an intention to use Font Awesome for icons, which is not fully implemented.

### Key Features

*   **Responsive Design:** The layout adapts to different screen sizes, from mobile phones to desktops, thanks to the Bootstrap framework.
*   **Interactive Components:** The website features a testimonials carousel on the homepage, which is powered by Bootstrap's JavaScript.
*   **Cookie Consent Banner:** A simple, non-intrusive banner at the bottom of the page informs users about cookie usage and provides an "Accept" button.
*   **Static Pages:** The website consists of several static pages, including:
    *   Home (`index.php`)
    *   About Us (`chi-siamo.html`)
    *   Services (`servizi.html`)
    *   Contact (`contatti.html`)
    *   Privacy Policy (`informativa-sulla-privacy.html`)
    *   Terms and Conditions (`termini-e-condizioni.html`)

### Contact Form

The contact page includes a contact form. However, the form is not functional. It is a standard HTML `<form>` element with no `action` attribute or associated JavaScript to handle form submission. To make it work, a back-end service or a third-party form provider would need to be integrated.

## File Structure

The project has a simple and flat file structure:

```
/
├── chi-siamo.html
├── contatti.html
├── index.php
├── informativa-sulla-privacy.html
├── servizi.html
├── termini-e-condizioni.html
└── images/
    ├── ... (numerous .jpeg files)
```

## Setup and Installation

This is a static website with no build process or dependencies to install. To run this project locally, you can simply open the `index.php` file in a web browser. For the PHP file to be parsed correctly (although it contains no PHP code), you might need to serve it from a local web server (like Apache or Nginx with PHP installed).

Alternatively, you can rename `index.php` to `index.html` and open it directly in your browser.

## No-Build Approach

This project follows a no-build approach. All libraries (Bootstrap and jQuery) are loaded directly from CDNs, and there is no need for a package manager (like npm or yarn) or a build tool (like Webpack or Parcel). This makes the project simple to deploy and maintain for basic static sites.
